@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    position: relative;
    overflow-x: hidden;
}

/* 动态背景粒子效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(1deg); }
    66% { transform: translateY(20px) rotate(-1deg); }
}

/* 通用样式 */
h1 {
    font-family: 'Orbitron', monospace;
    font-size: 32px;
    font-weight: 900;
    margin-bottom: 25px;
    color: #fff;
    text-align: center;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    letter-spacing: 2px;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5), 0 0 30px rgba(102, 126, 234, 0.5); }
    to { text-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 0 0 40px rgba(102, 126, 234, 0.8); }
}

h2 {
    font-family: 'Orbitron', monospace;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

h3 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #333;
    letter-spacing: 1px;
}

button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

button:hover::before {
    left: 100%;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* 大厅样式 */
.lobby {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 500px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.lobby-actions {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-top: 35px;
}

.join-room {
    display: flex;
    gap: 15px;
    align-items: center;
}

#room-id-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 25px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    outline: none;
}

#room-id-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

#room-id-input::placeholder {
    color: #999;
    font-style: italic;
}

/* 等待房间样式 */
.waiting-room {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 500px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInUp 0.6s ease-out;
}

.player-status {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 25px 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.player {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.player:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.player-name {
    font-weight: 500;
    color: #333;
    font-size: 16px;
}

.player-ready {
    color: #ff6b6b;
    font-weight: bold;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 5px 12px;
    border-radius: 20px;
    background: rgba(255, 107, 107, 0.1);
}

.player-ready.ready {
    color: #51cf66;
    background: rgba(81, 207, 102, 0.1);
}

#ready-button {
    margin-top: 25px;
    width: 100%;
    padding: 15px;
    font-size: 18px;
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
}

#ready-button.ready {
    background: linear-gradient(135deg, #ff6b6b 0%, #fa5252 100%);
}

#ready-button:hover {
    transform: translateY(-2px);
}

#waiting-message {
    margin-top: 25px;
    color: #666;
    font-style: italic;
    font-size: 16px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* 游戏容器样式 */
.game-container {
    width: 100%;
    max-width: 1200px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 30px;
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInUp 0.6s ease-out;
}

.player-boards {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    margin-bottom: 30px;
}

@media (max-width: 900px) {
    .player-boards {
        flex-direction: column;
        align-items: center;
        gap: 40px;
    }
}

.player-board {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.player-board:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.game-info {
    width: 100%;
    max-width: 220px;
    margin-bottom: 20px;
}

.score-container {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.score-container p {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.score-container span {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #667eea;
    font-size: 18px;
}

.next-piece-container {
    background: linear-gradient(135deg, rgba(255, 119, 198, 0.1) 0%, rgba(120, 219, 255, 0.1) 100%);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 119, 198, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.next-piece-container p {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

.next-piece {
    width: 120px;
    height: 120px;
    display: grid;
    grid-template-rows: repeat(4, 1fr);
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    padding: 10px;
}

.game-board-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    position: relative;
}

.game-board {
    width: 280px;
    height: 560px;
    display: grid;
    grid-template-rows: repeat(20, 1fr);
    grid-template-columns: repeat(10, 1fr);
    gap: 2px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: 3px solid #667eea;
    border-radius: 10px;
    padding: 5px;
    box-shadow:
        0 0 20px rgba(102, 126, 234, 0.3),
        inset 0 0 20px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.game-board::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(102, 126, 234, 0.1) 50%, transparent 51%);
    background-size: 20px 20px;
    pointer-events: none;
}

.cell {
    width: 100%;
    height: 100%;
    border-radius: 3px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.2s ease;
}

.cell.filled {
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow:
        0 0 10px rgba(255, 255, 255, 0.2),
        inset 0 0 10px rgba(255, 255, 255, 0.1);
    animation: blockGlow 0.3s ease-out;
}

@keyframes blockGlow {
    0% { transform: scale(1.1); box-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
    100% { transform: scale(1); }
}

/* 方块颜色 - 更鲜艳的霓虹色彩 */
.cell.color-1 {
    background: linear-gradient(135deg, #ff006e 0%, #ff4081 100%);
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.5);
} /* I 方块 */

.cell.color-2 {
    background: linear-gradient(135deg, #00d4ff 0%, #40c4ff 100%);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
} /* J 方块 */

.cell.color-3 {
    background: linear-gradient(135deg, #00ff88 0%, #4caf50 100%);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
} /* L 方块 */

.cell.color-4 {
    background: linear-gradient(135deg, #ff3d71 0%, #e91e63 100%);
    box-shadow: 0 0 15px rgba(255, 61, 113, 0.5);
} /* O 方块 */

.cell.color-5 {
    background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);
    box-shadow: 0 0 15px rgba(255, 149, 0, 0.5);
} /* S 方块 */

.cell.color-6 {
    background: linear-gradient(135deg, #ffeb3b 0%, #ffc107 100%);
    box-shadow: 0 0 15px rgba(255, 235, 59, 0.5);
} /* T 方块 */

.cell.color-7 {
    background: linear-gradient(135deg, #3f51b5 0%, #2196f3 100%);
    box-shadow: 0 0 15px rgba(63, 81, 181, 0.5);
} /* Z 方块 */

.game-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
    margin-top: 30px;
}

#start-button {
    width: 250px;
    padding: 15px;
    font-size: 18px;
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
    position: relative;
    overflow: hidden;
}

#start-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

#start-button:active::after {
    width: 300px;
    height: 300px;
}

.instructions {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 25px;
    border-radius: 15px;
    width: 100%;
    max-width: 450px;
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.instructions h3 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #667eea;
}

.instructions p {
    margin-bottom: 10px;
    font-size: 15px;
    color: #555;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    margin-bottom: 8px;
    font-family: 'Orbitron', monospace;
}

.instructions p:last-child {
    margin-bottom: 0;
}

/* 游戏结束样式 */
.game-over {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.game-over-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 450px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    animation: slideInScale 0.6s ease-out;
}

@keyframes slideInScale {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(30px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.game-over h2 {
    font-size: 32px;
    margin-bottom: 25px;
    color: #333;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-over p {
    font-size: 20px;
    margin-bottom: 25px;
    color: #555;
}

.game-over span {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: #667eea;
    font-size: 24px;
}

#play-again {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
    padding: 15px 30px;
    font-size: 18px;
    width: 100%;
}

/* 连接状态样式 */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    backdrop-filter: blur(10px);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    z-index: 100;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 错误消息样式 */
.error-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 30px;
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    text-align: center;
    z-index: 100;
    max-width: 450px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInScale 0.5s ease-out;
}

.error-message p {
    color: #ff6b6b;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 500;
}

#error-close {
    background: linear-gradient(135deg, #ff6b6b 0%, #fa5252 100%);
    width: 100%;
    padding: 12px;
}

/* 昵称输入界面样式 */
.nickname-input {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20;
    animation: fadeIn 0.5s ease-out;
}

.nickname-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 450px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    animation: slideInScale 0.6s ease-out;
}

#nickname {
    width: 100%;
    padding: 15px 20px;
    margin: 25px 0;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 25px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    outline: none;
}

#nickname:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

#nickname::placeholder {
    color: #999;
    font-style: italic;
}

#confirm-nickname {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#confirm-nickname:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .game-board {
        width: 220px;
        height: 440px;
    }

    .player-boards {
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }

    .player-board {
        margin-bottom: 0;
        padding: 20px;
    }

    .join-room {
        flex-direction: column;
        gap: 15px;
    }

    .game-container {
        padding: 20px;
    }

    .lobby, .waiting-room, .nickname-content {
        padding: 30px 25px;
    }

    h1 {
        font-size: 28px;
    }

    h2 {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .game-board {
        width: 200px;
        height: 400px;
    }

    .lobby, .waiting-room, .nickname-content {
        padding: 25px 20px;
    }

    .game-container {
        padding: 15px;
    }

    .player-board {
        padding: 15px;
    }

    .instructions p {
        font-size: 13px;
        padding: 6px 10px;
    }
}

/* 特殊效果 */
.cell.clearing {
    animation: clearLine 0.5s ease-out;
}

@keyframes clearLine {
    0% { background: white; transform: scale(1); }
    50% { background: #ffeb3b; transform: scale(1.1); }
    100% { background: transparent; transform: scale(0); }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 游戏板边框发光效果 */
.game-board.active {
    border-color: #51cf66;
    box-shadow:
        0 0 30px rgba(81, 207, 102, 0.5),
        inset 0 0 20px rgba(0, 0, 0, 0.2);
    animation: boardPulse 2s ease-in-out infinite;
}

@keyframes boardPulse {
    0%, 100% {
        border-color: #51cf66;
        box-shadow: 0 0 30px rgba(81, 207, 102, 0.5), inset 0 0 20px rgba(0, 0, 0, 0.2);
    }
    50% {
        border-color: #40c057;
        box-shadow: 0 0 40px rgba(64, 192, 87, 0.7), inset 0 0 20px rgba(0, 0, 0, 0.2);
    }
}

/* 对手游戏板样式 */
#opponent-board {
    opacity: 0.8;
    filter: brightness(0.9);
}

/* 方块下落动画 */
.cell.falling {
    animation: fallDown 0.3s ease-out;
}

@keyframes fallDown {
    from { transform: translateY(-20px); opacity: 0.7; }
    to { transform: translateY(0); opacity: 1; }
}

/* 分数增加动画 */
.score-increase {
    animation: scoreUp 0.5s ease-out;
}

@keyframes scoreUp {
    0% { transform: scale(1); color: inherit; }
    50% { transform: scale(1.2); color: #51cf66; }
    100% { transform: scale(1); color: inherit; }
}

/* 等级提升效果 */
.level-up {
    animation: levelUpGlow 1s ease-out;
}

@keyframes levelUpGlow {
    0% {
        transform: scale(1);
        color: inherit;
        text-shadow: none;
    }
    50% {
        transform: scale(1.3);
        color: #ffeb3b;
        text-shadow: 0 0 20px rgba(255, 235, 59, 0.8);
    }
    100% {
        transform: scale(1);
        color: inherit;
        text-shadow: none;
    }
}

/* 游戏暂停覆盖层 */
.game-paused {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 2px;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease-out;
}

/* 连击效果 */
.combo-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ffeb3b;
    font-size: 20px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.8);
    animation: comboFloat 1s ease-out forwards;
    pointer-events: none;
    z-index: 10;
}

@keyframes comboFloat {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -150%) scale(1.5);
    }
}

/* 胜利/失败状态样式 */
.game-result.win {
    color: #51cf66;
    animation: winCelebration 1s ease-out;
}

.game-result.lose {
    color: #ff6b6b;
    animation: loseShake 0.5s ease-out;
}

@keyframes winCelebration {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

@keyframes loseShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}