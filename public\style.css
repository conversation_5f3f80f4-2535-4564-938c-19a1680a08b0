* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

/* 通用样式 */
h1 {
    font-size: 28px;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

h2 {
    font-size: 22px;
    margin-bottom: 15px;
    color: #333;
    text-align: center;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    background-color: #4CAF50;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #45a049;
}

/* 大厅样式 */
.lobby {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    text-align: center;
}

.lobby-actions {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 30px;
}

.join-room {
    display: flex;
    gap: 10px;
}

#room-id-input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

/* 等待房间样式 */
.waiting-room {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    text-align: center;
}

.player-status {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 8px;
}

.player {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.player-ready {
    color: #f44336;
    font-weight: bold;
}

.player-ready.ready {
    color: #4CAF50;
}

#ready-button {
    margin-top: 20px;
    width: 100%;
    padding: 12px;
}

#ready-button.ready {
    background-color: #f44336;
}

#waiting-message {
    margin-top: 20px;
    color: #666;
    font-style: italic;
}

/* 游戏容器样式 */
.game-container {
    width: 100%;
    max-width: 1000px;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.player-boards {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 20px;
}

@media (max-width: 900px) {
    .player-boards {
        flex-direction: column;
        align-items: center;
    }
}

.player-board {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.game-info {
    width: 100%;
    max-width: 200px;
    margin-bottom: 15px;
}

.score-container {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.score-container p {
    margin-bottom: 8px;
    font-size: 16px;
}

.next-piece-container {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.next-piece-container p {
    margin-bottom: 10px;
    font-size: 16px;
}

.next-piece {
    width: 100px;
    height: 100px;
    display: grid;
    grid-template-rows: repeat(4, 1fr);
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;
    margin: 0 auto;
}

.game-board-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.game-board {
    width: 250px;
    height: 500px;
    display: grid;
    grid-template-rows: repeat(20, 1fr);
    grid-template-columns: repeat(10, 1fr);
    gap: 1px;
    background-color: #ddd;
    border: 2px solid #333;
}

.cell {
    width: 100%;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.cell.filled {
    border: 1px solid rgba(0, 0, 0, 0.3);
}

/* 方块颜色 */
.cell.color-1 { background-color: #FF0D72; } /* I 方块 */
.cell.color-2 { background-color: #0DC2FF; } /* J 方块 */
.cell.color-3 { background-color: #0DFF72; } /* L 方块 */
.cell.color-4 { background-color: #F538FF; } /* O 方块 */
.cell.color-5 { background-color: #FF8E0D; } /* S 方块 */
.cell.color-6 { background-color: #FFE138; } /* T 方块 */
.cell.color-7 { background-color: #3877FF; } /* Z 方块 */

.game-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
}

#start-button {
    width: 200px;
}

.instructions {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 8px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.instructions h3 {
    margin-bottom: 10px;
    font-size: 16px;
}

.instructions p {
    margin-bottom: 5px;
    font-size: 14px;
}

/* 游戏结束样式 */
.game-over {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.game-over-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.game-over h2 {
    font-size: 28px;
    margin-bottom: 20px;
    color: #333;
}

.game-over p {
    font-size: 18px;
    margin-bottom: 20px;
}

#play-again {
    background-color: #4CAF50;
    padding: 12px 24px;
    font-size: 18px;
}

/* 连接状态样式 */
.connection-status {
    position: fixed;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 100;
}

/* 错误消息样式 */
.error-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    text-align: center;
    z-index: 100;
    max-width: 400px;
    width: 90%;
}

.error-message p {
    color: #f44336;
    margin-bottom: 15px;
    font-size: 16px;
}

#error-close {
    background-color: #f44336;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-board {
        width: 200px;
        height: 400px;
    }
    
    .player-boards {
        flex-direction: column;
        align-items: center;
    }
    
    .player-board {
        margin-bottom: 30px;
    }
    
    .join-room {
        flex-direction: column;
    }
}
/* 昵称输入界面样式 */
.nickname-input {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20;
}

.nickname-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

#nickname {
    width: 100%;
    padding: 10px;
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

#confirm-nickname {
    width: 100%;
    padding: 12px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#confirm-nickname:hover {
    background-color: #45a049;
}