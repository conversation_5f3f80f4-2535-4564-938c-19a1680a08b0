// 玩家昵称
    let nickname = '';
    
    // 获取DOM元素 - 昵称输入
    const nicknameInput = document.getElementById('nickname-input');
    const nicknameInputField = document.getElementById('nickname');
    const confirmNicknameButton = document.getElementById('confirm-nickname');
document.addEventListener('DOMContentLoaded', () => {
    // 游戏常量
    const COLS = 10;
    const ROWS = 20;
    const EMPTY = 0;
    
    // WebSocket连接
    let socket;
    let playerId;
    let roomId;
    let isPlayer1 = false;
    let isReady = false;
    let opponentReady = false;
    let gameStarted = false;
    
    // 游戏状态
    let score = 0;
    let level = 1;
    let lines = 0;
    let isGameOver = false;
    let isPaused = false;
    let gameInterval;
    let speed = 1000; // 初始下落速度（毫秒）
    
    // 获取DOM元素 - 大厅
    const lobby = document.getElementById('lobby');
    const createRoomButton = document.getElementById('create-room-button');
    const joinRoomButton = document.getElementById('join-room-button');
    const roomIdInput = document.getElementById('room-id-input');
    
    // 获取DOM元素 - 等待房间
    const waitingRoom = document.getElementById('waiting-room');
    const roomIdDisplay = document.getElementById('room-id-display');
    const player1Status = document.getElementById('player1-status');
    const player2Status = document.getElementById('player2-status');
    const player1Ready = document.getElementById('player1-ready');
    const player2Ready = document.getElementById('player2-ready');
    const readyButton = document.getElementById('ready-button');
    const waitingMessage = document.getElementById('waiting-message');
    
    // 获取DOM元素 - 游戏界面
    const gameContainer = document.getElementById('game-container');
    const gameBoard = document.getElementById('game-board');
    const opponentBoard = document.getElementById('opponent-board');
    const nextPieceDisplay = document.getElementById('next-piece');
    const scoreDisplay = document.getElementById('score');
    const levelDisplay = document.getElementById('level');
    const linesDisplay = document.getElementById('lines');
    const opponentScoreDisplay = document.getElementById('opponent-score');
    const opponentLevelDisplay = document.getElementById('opponent-level');
    const opponentLinesDisplay = document.getElementById('opponent-lines');
    const startButton = document.getElementById('start-button');
    
    // 获取DOM元素 - 游戏结束
    const gameOverScreen = document.getElementById('game-over');
    const gameResult = document.getElementById('game-result');
    const finalScoreDisplay = document.getElementById('final-score');
    const playAgainButton = document.getElementById('play-again');
    
    // 获取DOM元素 - 连接状态和错误
    const connectionStatus = document.getElementById('connection-status');
    const errorMessage = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');
    const errorClose = document.getElementById('error-close');
    
    // 定义俄罗斯方块形状
    const SHAPES = [
        [], // 空数组，使索引从1开始，方便与颜色对应
        [ // I 形方块
            [0, 0, 0, 0],
            [1, 1, 1, 1],
            [0, 0, 0, 0],
            [0, 0, 0, 0]
        ],
        [ // J 形方块
            [2, 0, 0],
            [2, 2, 2],
            [0, 0, 0]
        ],
        [ // L 形方块
            [0, 0, 3],
            [3, 3, 3],
            [0, 0, 0]
        ],
        [ // O 形方块
            [4, 4],
            [4, 4]
        ],
        [ // S 形方块
            [0, 5, 5],
            [5, 5, 0],
            [0, 0, 0]
        ],
        [ // T 形方块
            [0, 6, 0],
            [6, 6, 6],
            [0, 0, 0]
        ],
        [ // Z 形方块
            [7, 7, 0],
            [0, 7, 7],
            [0, 0, 0]
        ]
    ];
    
    // 创建游戏板
    let board = Array.from({ length: ROWS }, () => Array(COLS).fill(EMPTY));
    let opponentBoardState = Array.from({ length: ROWS }, () => Array(COLS).fill(EMPTY));
    
    // 当前方块和下一个方块
    let currentPiece = null;
    let nextPiece = null;
    
    // 初始化
    function init() {
        // 连接WebSocket
        connectWebSocket();
        
        // 添加事件监听器
        createRoomButton.addEventListener('click', createRoom);
        joinRoomButton.addEventListener('click', joinRoom);
        readyButton.addEventListener('click', toggleReady);
        startButton.addEventListener('click', togglePause);
        playAgainButton.addEventListener('click', returnToLobby);
        errorClose.addEventListener('click', closeError);
        document.addEventListener('keydown', handleKeyPress);
    }
    
// 处理昵称确认
    confirmNicknameButton.addEventListener('click', confirmNickname);
    
    // 按Enter键确认昵称
    nicknameInputField.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            confirmNickname();
        }
    });
    
    // 确认昵称
    function confirmNickname() {
        const inputNickname = nicknameInputField.value.trim();
        
        if (!inputNickname) {
            alert('请输入昵称');
            return;
        }
        
        nickname = inputNickname;
        
        // 隐藏昵称输入界面，显示大厅
        nicknameInput.style.display = 'none';
        lobby.style.display = 'block';
    }
    // 连接WebSocket
    function connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        const port = window.location.port || (protocol === 'wss:' ? '443' : '80');
        const wsUrl = `${protocol}//${host}:${port}`;
        
        connectionStatus.style.display = 'block';
        connectionStatus.querySelector('p').textContent = '正在连接服务器...';
        
        socket = new WebSocket(wsUrl);
        
        socket.onopen = () => {
            connectionStatus.querySelector('p').textContent = '已连接';
            setTimeout(() => {
                connectionStatus.style.display = 'none';
            }, 2000);
        };
        
        socket.onclose = () => {
            connectionStatus.style.display = 'block';
            connectionStatus.querySelector('p').textContent = '连接已断开，请刷新页面重试';
        };
        
        socket.onerror = (error) => {
            showError('连接错误，请刷新页面重试');
            console.error('WebSocket错误:', error);
        };
        
        socket.onmessage = (event) => {
            handleMessage(JSON.parse(event.data));
        };
    }
    
    // 处理WebSocket消息
    function handleMessage(data) {
        console.log('收到消息:', data);
        
        switch (data.type) {
            case 'connected':
                playerId = data.playerId;
                break;
                
            case 'room_created':
                handleRoomCreated(data);
                break;
                
            case 'room_joined':
                handleRoomJoined(data);
                break;
                
            case 'player_joined':
                handlePlayerJoined(data);
                break;
                
            case 'player_ready':
                handlePlayerReady(data);
                break;
                
            case 'game_start':
                handleGameStart();
                break;
                
            case 'game_update':
                handleGameUpdate(data);
                break;
                
            case 'game_over':
                handleOpponentGameOver(data);
                break;
                
            case 'player_disconnected':
                handlePlayerDisconnected(data);
                break;
                
            case 'error':
                showError(data.message);
                break;
        }
    }
    
    // 创建房间
    function createRoom() {
        socket.send(JSON.stringify({
            type: 'create_room'
        }));
    }
    
    // 加入房间
    function joinRoom() {
        const roomIdToJoin = roomIdInput.value.trim().toUpperCase();
        
        if (!roomIdToJoin) {
            showError('请输入房间ID');
            return;
        }
        
        socket.send(JSON.stringify({
            type: 'join_room',
            roomId: roomIdToJoin
        }));
    }
    
    // 处理房间创建成功
    function handleRoomCreated(data) {
        roomId = data.roomId;
        isPlayer1 = true;
        
        // 显示等待房间
        lobby.style.display = 'none';
        waitingRoom.style.display = 'block';
        
        // 更新房间信息
        roomIdDisplay.textContent = roomId;
        player1Status.querySelector('.player-name').textContent = `${nickname} (你)`;
        player2Status.querySelector('.player-name').textContent = '等待玩家加入...';
        waitingMessage.textContent = '等待另一位玩家加入...';
    }
    
    // 处理加入房间成功
    function handleRoomJoined(data) {
        roomId = data.roomId;
        isPlayer1 = data.isPlayer1;
        
        // 显示等待房间
        lobby.style.display = 'none';
        waitingRoom.style.display = 'block';
        
        // 更新房间信息
        roomIdDisplay.textContent = roomId;
        
        if (isPlayer1) {
            player1Status.querySelector('.player-name').textContent = `${nickname} (你)`;
            player2Status.querySelector('.player-name').textContent = '玩家2';
        } else {
            player1Status.querySelector('.player-name').textContent = '玩家1';
            player2Status.querySelector('.player-name').textContent = `玩家2 (你)`;
        }
        
        waitingMessage.textContent = '请准备开始游戏';
    }
    
    // 处理玩家加入
    function handlePlayerJoined(data) {
        player2Status.querySelector('.player-name').textContent = '玩家2';
        waitingMessage.textContent = '请准备开始游戏';
    }
    
    // 切换准备状态
    function toggleReady() {
        isReady = !isReady;
        
        if (isReady) {
            readyButton.textContent = '取消准备';
            readyButton.classList.add('ready');
            
            if (isPlayer1) {
                player1Ready.textContent = '已准备';
                player1Ready.classList.add('ready');
            } else {
                player2Ready.textContent = '已准备';
                player2Ready.classList.add('ready');
            }
            
            // 发送准备消息
            socket.send(JSON.stringify({
                type: 'ready',
                roomId: roomId
            }));
        } else {
            readyButton.textContent = '准备';
            readyButton.classList.remove('ready');
            
            if (isPlayer1) {
                player1Ready.textContent = '未准备';
                player1Ready.classList.remove('ready');
            } else {
                player2Ready.textContent = '未准备';
                player2Ready.classList.remove('ready');
            }
            
            // TODO: 发送取消准备消息（服务器端需要支持）
        }
    }
    
    // 处理玩家准备
    function handlePlayerReady(data) {
        if ((isPlayer1 && data.playerId !== playerId) || (!isPlayer1 && data.playerId !== playerId)) {
            opponentReady = true;
            
            if (isPlayer1) {
                player2Ready.textContent = '已准备';
                player2Ready.classList.add('ready');
            } else {
                player1Ready.textContent = '已准备';
                player1Ready.classList.add('ready');
            }
        }
        
        // 检查是否双方都准备好了
        if (isReady && opponentReady) {
            waitingMessage.textContent = '双方已准备，游戏即将开始...';
        }
    }
// 处理游戏开始
    function handleGameStart() {
        gameStarted = true;
        
        // 隐藏等待房间，显示游戏界面
        waitingRoom.style.display = 'none';
        gameContainer.style.display = 'block';
        
        // 初始化游戏
        initGame();
    }
    
    // 初始化游戏
    function initGame() {
        // 重置游戏状态
        score = 0;
        level = 1;
        lines = 0;
        isGameOver = false;
        isPaused = false;
        speed = 1000;
        
        // 清空游戏板
        board = Array.from({ length: ROWS }, () => Array(COLS).fill(EMPTY));
        opponentBoardState = Array.from({ length: ROWS }, () => Array(COLS).fill(EMPTY));
        
        // 创建游戏板格子
        createBoard(gameBoard);
        createBoard(opponentBoard);
        
        // 创建下一个方块预览区域
        createNextPieceDisplay();
        
        // 更新显示
        scoreDisplay.textContent = score;
        levelDisplay.textContent = level;
        linesDisplay.textContent = lines;
        opponentScoreDisplay.textContent = '0';
        opponentLevelDisplay.textContent = '1';
        opponentLinesDisplay.textContent = '0';
        
        // 生成第一个方块和下一个方块
        nextPiece = generatePiece();
        getNewPiece();
        
        // 开始游戏循环
        if (gameInterval) {
            clearInterval(gameInterval);
        }
        gameInterval = setInterval(gameLoop, speed);
    }
    
    // 创建游戏板格子
    function createBoard(boardElement) {
        boardElement.innerHTML = '';
        boardElement.style.gridTemplateRows = `repeat(${ROWS}, 1fr)`;
        boardElement.style.gridTemplateColumns = `repeat(${COLS}, 1fr)`;
        
        for (let r = 0; r < ROWS; r++) {
            for (let c = 0; c < COLS; c++) {
                const cell = document.createElement('div');
                cell.classList.add('cell');
                cell.setAttribute('data-row', r);
                cell.setAttribute('data-col', c);
                boardElement.appendChild(cell);
            }
        }
    }
    
    // 创建下一个方块预览区域
    function createNextPieceDisplay() {
        nextPieceDisplay.innerHTML = '';
        nextPieceDisplay.style.gridTemplateRows = `repeat(4, 1fr)`;
        nextPieceDisplay.style.gridTemplateColumns = `repeat(4, 1fr)`;
        
        for (let r = 0; r < 4; r++) {
            for (let c = 0; c < 4; c++) {
                const cell = document.createElement('div');
                cell.classList.add('cell');
                nextPieceDisplay.appendChild(cell);
            }
        }
    }
    
    // 生成随机方块
    function generatePiece() {
        const shapeIndex = Math.floor(Math.random() * 7) + 1; // 1-7
        const shape = SHAPES[shapeIndex];
        
        return {
            shape,
            color: shapeIndex,
            row: 0,
            col: Math.floor((COLS - shape[0].length) / 2)
        };
    }
    
    // 获取新方块
    function getNewPiece() {
        currentPiece = nextPiece;
        nextPiece = generatePiece();
        
        // 显示下一个方块
        drawNextPiece();
        
        // 检查游戏是否结束
        if (checkCollision(currentPiece)) {
            gameOver();
        }
    }
    
    // 绘制下一个方块
    function drawNextPiece() {
        // 清除预览区域
        const cells = nextPieceDisplay.querySelectorAll('.cell');
        cells.forEach(cell => {
            cell.className = 'cell';
        });
        
        // 绘制下一个方块
        const shape = nextPiece.shape;
        const color = nextPiece.color;
        
        for (let r = 0; r < shape.length; r++) {
            for (let c = 0; c < shape[r].length; c++) {
                if (shape[r][c] !== 0) {
                    const index = r * 4 + c;
                    if (cells[index]) {
                        cells[index].classList.add('filled', `color-${color}`);
                    }
                }
            }
        }
    }
    
    // 绘制游戏板
    function drawBoard() {
        const cells = gameBoard.querySelectorAll('.cell');
        
        // 更新游戏板
        for (let r = 0; r < ROWS; r++) {
            for (let c = 0; c < COLS; c++) {
                const index = r * COLS + c;
                const value = board[r][c];
                
                // 清除之前的类
                cells[index].className = 'cell';
                
                if (value !== EMPTY) {
                    cells[index].classList.add('filled', `color-${value}`);
                }
            }
        }
        
        // 绘制当前方块
        if (currentPiece) {
            const shape = currentPiece.shape;
            const color = currentPiece.color;
            const row = currentPiece.row;
            const col = currentPiece.col;
            
            for (let r = 0; r < shape.length; r++) {
                for (let c = 0; c < shape[r].length; c++) {
                    if (shape[r][c] !== 0) {
                        const boardRow = row + r;
                        const boardCol = col + c;
                        
                        if (boardRow >= 0 && boardRow < ROWS && boardCol >= 0 && boardCol < COLS) {
                            const index = boardRow * COLS + boardCol;
                            cells[index].classList.add('filled', `color-${color}`);
                        }
                    }
                }
            }
        }
        
        // 发送游戏状态更新
        sendGameUpdate();
    }
    
    // 绘制对手的游戏板
    function drawOpponentBoard() {
        const cells = opponentBoard.querySelectorAll('.cell');
        
        // 更新对手游戏板
        for (let r = 0; r < ROWS; r++) {
            for (let c = 0; c < COLS; c++) {
                const index = r * COLS + c;
                const value = opponentBoardState[r][c];
                
                // 清除之前的类
                cells[index].className = 'cell';
                
                if (value !== EMPTY) {
                    cells[index].classList.add('filled', `color-${value}`);
                }
            }
        }
    }
    
    // 发送游戏状态更新
    function sendGameUpdate() {
        if (!gameStarted || isGameOver) return;
        
        socket.send(JSON.stringify({
            type: 'game_update',
            roomId: roomId,
            gameState: {
                board: board,
                score: score,
                level: level,
                lines: lines
            }
        }));
    }
    
    // 处理游戏状态更新
    function handleGameUpdate(data) {
        if (data.playerId === playerId) return;
        
        // 更新对手的游戏状态
        opponentBoardState = data.gameState.board;
        opponentScoreDisplay.textContent = data.gameState.score;
        opponentLevelDisplay.textContent = data.gameState.level;
        opponentLinesDisplay.textContent = data.gameState.lines;
        
        // 绘制对手的游戏板
        drawOpponentBoard();
    }
    
    // 检查碰撞
    function checkCollision(piece) {
        const shape = piece.shape;
        const row = piece.row;
        const col = piece.col;
        
        for (let r = 0; r < shape.length; r++) {
            for (let c = 0; c < shape[r].length; c++) {
                if (shape[r][c] !== 0) {
                    const boardRow = row + r;
                    const boardCol = col + c;
                    
                    // 检查是否超出边界或与其他方块碰撞
                    if (
                        boardRow >= ROWS || 
                        boardCol < 0 || 
                        boardCol >= COLS || 
                        (boardRow >= 0 && board[boardRow][boardCol] !== EMPTY)
                    ) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    // 锁定方块
    function lockPiece() {
        const shape = currentPiece.shape;
        const color = currentPiece.color;
        const row = currentPiece.row;
        const col = currentPiece.col;
        
        for (let r = 0; r < shape.length; r++) {
            for (let c = 0; c < shape[r].length; c++) {
                if (shape[r][c] !== 0) {
                    const boardRow = row + r;
                    const boardCol = col + c;
                    
                    if (boardRow >= 0 && boardRow < ROWS && boardCol >= 0 && boardCol < COLS) {
                        board[boardRow][boardCol] = color;
                    }
                }
            }
        }
        
        // 检查并清除已完成的行
        clearLines();
        
        // 获取新方块
        getNewPiece();
    }
    
    // 移动方块
    function movePiece(rowOffset, colOffset) {
        if (isPaused || isGameOver) return false;
        
        const newPiece = {
            ...currentPiece,
            row: currentPiece.row + rowOffset,
            col: currentPiece.col + colOffset
        };
        
        if (!checkCollision(newPiece)) {
            currentPiece = newPiece;
            drawBoard();
            return true;
        }
        
        // 如果是向下移动并且发生碰撞，则锁定方块
        if (rowOffset > 0) {
            lockPiece();
        }
        
        return false;
    }
    
    // 旋转方块
    function rotatePiece() {
        if (isPaused || isGameOver) return false;
        
        // 创建新的旋转后的形状
        const shape = currentPiece.shape;
        const newShape = Array.from({ length: shape[0].length }, () => Array(shape.length).fill(0));
        
        for (let r = 0; r < shape.length; r++) {
            for (let c = 0; c < shape[r].length; c++) {
                newShape[c][shape.length - 1 - r] = shape[r][c];
            }
        }
        
        const newPiece = {
            ...currentPiece,
            shape: newShape
        };
        
        // 检查旋转后是否会发生碰撞
        if (!checkCollision(newPiece)) {
            currentPiece = newPiece;
            drawBoard();
            return true;
        }
        
        // 尝试墙踢（wall kick）- 左右移动一格再尝试旋转
        for (let offset of [-1, 1, -2, 2]) {
            const kickedPiece = {
                ...newPiece,
                col: newPiece.col + offset
            };
            
            if (!checkCollision(kickedPiece)) {
                currentPiece = kickedPiece;
                drawBoard();
                return true;
            }
        }
        
        return false;
    }
    
    // 硬降（直接落到底部）
    function hardDrop() {
        if (isPaused || isGameOver) return;
        
        let dropDistance = 0;
        
        while (!checkCollision({
            ...currentPiece,
            row: currentPiece.row + dropDistance + 1
        })) {
            dropDistance++;
        }
        
        if (dropDistance > 0) {
            currentPiece.row += dropDistance;
            drawBoard();
            lockPiece();
        }
    }
    
    // 清除已完成的行
    function clearLines() {
        let linesCleared = 0;
        
        for (let r = ROWS - 1; r >= 0; r--) {
            if (board[r].every(cell => cell !== EMPTY)) {
                // 清除该行
                board.splice(r, 1);
                // 在顶部添加新行
                board.unshift(Array(COLS).fill(EMPTY));
                linesCleared++;
                r++; // 重新检查当前行（现在是新的一行）
            }
        }
        
        if (linesCleared > 0) {
            // 更新分数和等级
            updateScore(linesCleared);
        }
    }
    
    // 更新分数
    function updateScore(linesCleared) {
        // 根据消除的行数计算分数
        const points = [0, 40, 100, 300, 1200]; // 0, 1, 2, 3, 4行的分数
        score += points[linesCleared] * level;
        lines += linesCleared;
        
        // 每消除10行提高一个等级
        level = Math.floor(lines / 10) + 1;
        
        // 更新速度
        speed = Math.max(100, 1000 - (level - 1) * 100);
        
        // 如果游戏正在运行，重新设置间隔
        if (gameInterval) {
            clearInterval(gameInterval);
            gameInterval = setInterval(gameLoop, speed);
        }
        
        // 更新显示
        scoreDisplay.textContent = score;
        levelDisplay.textContent = level;
        linesDisplay.textContent = lines;
    }
    
    // 游戏循环
    function gameLoop() {
        if (!isPaused && !isGameOver) {
            movePiece(1, 0); // 向下移动一格
        }
    }
    
    // 处理键盘按键
    function handleKeyPress(event) {
        if (!gameStarted || isPaused || isGameOver) return;
        
        switch (event.keyCode) {
            case 37: // 左箭头
                movePiece(0, -1);
                break;
            case 39: // 右箭头
                movePiece(0, 1);
                break;
            case 40: // 下箭头
                movePiece(1, 0);
                break;
            case 38: // 上箭头
                rotatePiece();
                break;
            case 32: // 空格
                hardDrop();
                break;
        }
    }
    
    // 切换暂停/继续
    function togglePause() {
        if (!gameStarted || isGameOver) return;
        
        isPaused = !isPaused;
        startButton.textContent = isPaused ? '继续' : '暂停';
        
        if (isPaused) {
            clearInterval(gameInterval);
        } else {
            gameInterval = setInterval(gameLoop, speed);
        }
    }
    
    // 游戏结束
    function gameOver() {
        isGameOver = true;
        clearInterval(gameInterval);
        
        // 发送游戏结束消息
        socket.send(JSON.stringify({
            type: 'game_over',
            roomId: roomId
        }));
        
        // 显示游戏结束屏幕
        finalScoreDisplay.textContent = score;
        gameResult.textContent = '你输了!';
        gameOverScreen.style.display = 'flex';
    }
    
    // 处理对手游戏结束
    function handleOpponentGameOver(data) {
        if (data.playerId === playerId) return;
        
        // 如果对手游戏结束，显示胜利消息
        if (!isGameOver) {
            isGameOver = true;
            clearInterval(gameInterval);
            
            finalScoreDisplay.textContent = score;
            gameResult.textContent = '你赢了!';
            gameOverScreen.style.display = 'flex';
        }
    }
    
    // 处理玩家断开连接
    function handlePlayerDisconnected(data) {
        if (gameStarted) {
            // 如果游戏已经开始，显示胜利消息
            isGameOver = true;
            clearInterval(gameInterval);
            
            finalScoreDisplay.textContent = score;
            gameResult.textContent = '对手断开连接，你赢了!';
            gameOverScreen.style.display = 'flex';
        } else {
            // 如果游戏还没开始，返回大厅
            showError('对手断开连接');
            returnToLobby();
        }
    }
    
    // 返回大厅
    function returnToLobby() {
        // 重置状态
        roomId = null;
        isPlayer1 = false;
        isReady = false;
        opponentReady = false;
        gameStarted = false;
        isGameOver = false;
        isPaused = false;
        
        if (gameInterval) {
            clearInterval(gameInterval);
            gameInterval = null;
        }
        
        // 隐藏游戏界面和等待房间，显示大厅
        gameContainer.style.display = 'none';
        waitingRoom.style.display = 'none';
        gameOverScreen.style.display = 'none';
        lobby.style.display = 'block';
    }
    
    // 显示错误消息
    function showError(message) {
        errorText.textContent = message;
        errorMessage.style.display = 'block';
    }
    
    // 关闭错误消息
    function closeError() {
        errorMessage.style.display = 'none';
    }
    
    // 初始化
    init();
});