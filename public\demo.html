<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetris样式演示</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .demo-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .sample-board {
            width: 200px;
            height: 400px;
            display: grid;
            grid-template-rows: repeat(16, 1fr);
            grid-template-columns: repeat(8, 1fr);
            gap: 2px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border: 3px solid #667eea;
            border-radius: 10px;
            padding: 5px;
            box-shadow: 
                0 0 20px rgba(102, 126, 234, 0.3),
                inset 0 0 20px rgba(0, 0, 0, 0.2);
            margin: 0 auto;
        }
        
        .demo-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }
        
        .color-showcase {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }
        
        .color-block {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 标题演示 -->
        <div class="demo-section">
            <h1>双人俄罗斯方块</h1>
            <h2>现代化UI设计演示</h2>
            <p style="text-align: center; color: #666; font-size: 16px;">
                全新的视觉设计，包含渐变背景、霓虹色彩、动画效果和现代化界面元素
            </p>
        </div>

        <!-- 按钮样式演示 -->
        <div class="demo-section">
            <h2>按钮样式</h2>
            <div class="demo-buttons">
                <button>创建房间</button>
                <button>加入房间</button>
                <button id="ready-button">准备</button>
                <button id="ready-button" class="ready">取消准备</button>
                <button id="start-button">开始/暂停</button>
                <button id="play-again">返回大厅</button>
                <button id="error-close">关闭</button>
            </div>
        </div>

        <!-- 游戏板和颜色演示 -->
        <div class="demo-section">
            <h2>游戏板设计</h2>
            <div class="demo-grid">
                <div>
                    <h3 style="text-align: center; margin-bottom: 15px;">游戏板样式</h3>
                    <div class="sample-board" id="sample-board">
                        <!-- 将通过JavaScript填充 -->
                    </div>
                </div>
                <div>
                    <h3 style="text-align: center; margin-bottom: 15px;">方块颜色</h3>
                    <div class="color-showcase">
                        <div class="color-block cell color-1" title="I方块"></div>
                        <div class="color-block cell color-2" title="J方块"></div>
                        <div class="color-block cell color-3" title="L方块"></div>
                        <div class="color-block cell color-4" title="O方块"></div>
                        <div class="color-block cell color-5" title="S方块"></div>
                        <div class="color-block cell color-6" title="T方块"></div>
                        <div class="color-block cell color-7" title="Z方块"></div>
                    </div>
                    <p style="text-align: center; margin-top: 15px; color: #666;">
                        每个方块都有独特的霓虹色彩和发光效果
                    </p>
                </div>
            </div>
        </div>

        <!-- 信息面板演示 -->
        <div class="demo-section">
            <h2>信息面板</h2>
            <div class="demo-grid">
                <div class="score-container">
                    <p>分数: <span>12,450</span></p>
                    <p>等级: <span>5</span></p>
                    <p>已消除行数: <span>23</span></p>
                </div>
                <div class="next-piece-container">
                    <p>下一个方块:</p>
                    <div class="next-piece" id="next-piece-demo">
                        <!-- 将通过JavaScript填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 玩家状态演示 -->
        <div class="demo-section">
            <h2>玩家状态</h2>
            <div class="player-status">
                <div class="player">
                    <span class="player-name">玩家1</span>
                    <span class="player-ready ready">已准备</span>
                </div>
                <div class="player">
                    <span class="player-name">玩家2</span>
                    <span class="player-ready">未准备</span>
                </div>
            </div>
        </div>

        <!-- 操作说明 -->
        <div class="demo-section">
            <div class="instructions">
                <h3>操作说明:</h3>
                <p>← → <span>左右移动</span></p>
                <p>↑ <span>旋转</span></p>
                <p>↓ <span>加速下落</span></p>
                <p>空格 <span>直接落到底部</span></p>
            </div>
        </div>
    </div>

    <script>
        // 填充示例游戏板
        function fillSampleBoard() {
            const board = document.getElementById('sample-board');
            const totalCells = 8 * 16; // 8列 x 16行
            
            for (let i = 0; i < totalCells; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                
                // 随机填充一些方块作为演示
                if (Math.random() > 0.7) {
                    const colorClass = `color-${Math.floor(Math.random() * 7) + 1}`;
                    cell.classList.add('filled', colorClass);
                }
                
                board.appendChild(cell);
            }
        }
        
        // 填充下一个方块演示
        function fillNextPieceDemo() {
            const nextPiece = document.getElementById('next-piece-demo');
            const totalCells = 4 * 4; // 4x4网格
            
            for (let i = 0; i < totalCells; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                
                // 创建一个T形方块作为演示
                if ([5, 9, 10, 11].includes(i)) {
                    cell.classList.add('filled', 'color-6');
                }
                
                nextPiece.appendChild(cell);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            fillSampleBoard();
            fillNextPieceDemo();
            
            // 添加一些动画效果
            setTimeout(() => {
                const board = document.getElementById('sample-board');
                board.classList.add('active');
            }, 1000);
        });
    </script>
</body>
</html>
