const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');

// 创建Express应用
const app = express();
const port = process.env.PORT || 3000;

// 提供静态文件
app.use(express.static(path.join(__dirname, '../public')));

// 创建HTTP服务器
const server = http.createServer(app);

// 创建WebSocket服务器
const wss = new WebSocket.Server({ server });

// 游戏房间管理
const rooms = new Map();

// 生成唯一的房间ID
function generateRoomId() {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// 处理WebSocket连接
wss.on('connection', (ws) => {
    console.log('新玩家连接');
    
    // 为玩家分配一个唯一ID
    const playerId = Math.random().toString(36).substring(2, 10);
    ws.playerId = playerId;
    
    // 发送玩家ID给客户端
    ws.send(JSON.stringify({
        type: 'connected',
        playerId: playerId
    }));
    
    // 处理来自客户端的消息
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            console.log('收到消息:', data);
            
            switch (data.type) {
                case 'create_room':
                    handleCreateRoom(ws);
                    break;
                    
                case 'join_room':
                    handleJoinRoom(ws, data.roomId);
                    break;
                    
                case 'ready':
                    handlePlayerReady(ws, data.roomId);
                    break;
                    
                case 'game_update':
                    handleGameUpdate(ws, data);
                    break;
                    
                case 'game_over':
                    handleGameOver(ws, data);
                    break;
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
        }
    });
    
    // 处理连接关闭
    ws.on('close', () => {
        console.log('玩家断开连接:', ws.playerId);
        
        // 如果玩家在房间中，通知房间中的其他玩家
        if (ws.roomId) {
            const room = rooms.get(ws.roomId);
            if (room) {
                // 从房间中移除玩家
                if (room.player1 && room.player1.playerId === ws.playerId) {
                    room.player1 = null;
                } else if (room.player2 && room.player2.playerId === ws.playerId) {
                    room.player2 = null;
                }
                
                // 如果房间中还有其他玩家，通知他们
                if (room.player1) {
                    room.player1.ws.send(JSON.stringify({
                        type: 'player_disconnected',
                        playerId: ws.playerId
                    }));
                }
                
                if (room.player2) {
                    room.player2.ws.send(JSON.stringify({
                        type: 'player_disconnected',
                        playerId: ws.playerId
                    }));
                }
                
                // 如果房间中没有玩家了，删除房间
                if (!room.player1 && !room.player2) {
                    rooms.delete(ws.roomId);
                    console.log('房间已删除:', ws.roomId);
                }
            }
        }
    });
});

// 处理创建房间
function handleCreateRoom(ws) {
    const roomId = generateRoomId();
    
    // 创建新房间
    rooms.set(roomId, {
        id: roomId,
        player1: {
            playerId: ws.playerId,
            ws: ws,
            ready: false
        },
        player2: null,
        gameStarted: false
    });
    
    // 将玩家与房间关联
    ws.roomId = roomId;
    
    // 通知玩家房间已创建
    ws.send(JSON.stringify({
        type: 'room_created',
        roomId: roomId,
        playerId: ws.playerId
    }));
    
    console.log('房间已创建:', roomId);
}

// 处理加入房间
function handleJoinRoom(ws, roomId) {
    // 检查房间是否存在
    if (!rooms.has(roomId)) {
        ws.send(JSON.stringify({
            type: 'error',
            message: '房间不存在'
        }));
        return;
    }
    
    const room = rooms.get(roomId);
    
    // 检查房间是否已满
    if (room.player1 && room.player2) {
        ws.send(JSON.stringify({
            type: 'error',
            message: '房间已满'
        }));
        return;
    }
    
    // 检查游戏是否已经开始
    if (room.gameStarted) {
        ws.send(JSON.stringify({
            type: 'error',
            message: '游戏已经开始'
        }));
        return;
    }
    
    // 将玩家添加到房间
    if (!room.player1) {
        room.player1 = {
            playerId: ws.playerId,
            ws: ws,
            ready: false
        };
    } else {
        room.player2 = {
            playerId: ws.playerId,
            ws: ws,
            ready: false
        };
    }
    
    // 将玩家与房间关联
    ws.roomId = roomId;
    
    // 通知玩家已加入房间
    ws.send(JSON.stringify({
        type: 'room_joined',
        roomId: roomId,
        playerId: ws.playerId,
        isPlayer1: room.player1.playerId === ws.playerId
    }));
    
    // 通知房间中的其他玩家
    if (room.player1 && room.player1.playerId !== ws.playerId) {
        room.player1.ws.send(JSON.stringify({
            type: 'player_joined',
            playerId: ws.playerId
        }));
    }
    
    if (room.player2 && room.player2.playerId !== ws.playerId) {
        room.player2.ws.send(JSON.stringify({
            type: 'player_joined',
            playerId: ws.playerId
        }));
    }
    
    console.log('玩家加入房间:', roomId, ws.playerId);
}

// 处理玩家准备
function handlePlayerReady(ws, roomId) {
    // 检查房间是否存在
    if (!rooms.has(roomId)) {
        return;
    }
    
    const room = rooms.get(roomId);
    
    // 更新玩家准备状态
    if (room.player1 && room.player1.playerId === ws.playerId) {
        room.player1.ready = true;
    } else if (room.player2 && room.player2.playerId === ws.playerId) {
        room.player2.ready = true;
    }
    
    // 通知房间中的所有玩家
    if (room.player1) {
        room.player1.ws.send(JSON.stringify({
            type: 'player_ready',
            playerId: ws.playerId
        }));
    }
    
    if (room.player2) {
        room.player2.ws.send(JSON.stringify({
            type: 'player_ready',
            playerId: ws.playerId
        }));
    }
    
    // 检查是否所有玩家都准备好了
    if (room.player1 && room.player2 && room.player1.ready && room.player2.ready) {
        // 开始游戏
        room.gameStarted = true;
        
        // 通知所有玩家游戏开始
        if (room.player1) {
            room.player1.ws.send(JSON.stringify({
                type: 'game_start'
            }));
        }
        
        if (room.player2) {
            room.player2.ws.send(JSON.stringify({
                type: 'game_start'
            }));
        }
        
        console.log('游戏开始:', roomId);
    }
}

// 处理游戏更新
function handleGameUpdate(ws, data) {
    // 检查房间是否存在
    if (!rooms.has(data.roomId)) {
        return;
    }
    
    const room = rooms.get(data.roomId);
    
    // 将游戏更新转发给房间中的其他玩家
    if (room.player1 && room.player1.playerId !== ws.playerId) {
        room.player1.ws.send(JSON.stringify({
            type: 'game_update',
            playerId: ws.playerId,
            gameState: data.gameState
        }));
    }
    
    if (room.player2 && room.player2.playerId !== ws.playerId) {
        room.player2.ws.send(JSON.stringify({
            type: 'game_update',
            playerId: ws.playerId,
            gameState: data.gameState
        }));
    }
}

// 处理游戏结束
function handleGameOver(ws, data) {
    // 检查房间是否存在
    if (!rooms.has(data.roomId)) {
        return;
    }
    
    const room = rooms.get(data.roomId);
    
    // 通知房间中的所有玩家
    if (room.player1) {
        room.player1.ws.send(JSON.stringify({
            type: 'game_over',
            playerId: ws.playerId
        }));
    }
    
    if (room.player2) {
        room.player2.ws.send(JSON.stringify({
            type: 'game_over',
            playerId: ws.playerId
        }));
    }
    
    console.log('游戏结束:', data.roomId, '玩家失败:', ws.playerId);
    
    // 重置房间状态
    room.gameStarted = false;
    if (room.player1) room.player1.ready = false;
    if (room.player2) room.player2.ready = false;
}

// 启动服务器
server.listen(port, () => {
    console.log(`服务器运行在 http://localhost:${port}`);
});