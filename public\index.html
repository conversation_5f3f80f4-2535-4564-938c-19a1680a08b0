<!-- 昵称输入界面 -->
    <div class="nickname-input" id="nickname-input">
        <div class="nickname-content">
            <h2>请输入你的昵称</h2>
            <input type="text" id="nickname" placeholder="输入昵称（最多8个字符）" maxlength="8">
            <button id="confirm-nickname">确认</button>
        </div>
    </div>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双人俄罗斯方块</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 房间创建/加入界面 -->
    <div class="lobby" id="lobby">
        <div class="lobby-content">
            <h1>双人俄罗斯方块</h1>
            <div class="lobby-actions">
                <button id="create-room-button">创建房间</button>
                <div class="join-room">
                    <input type="text" id="room-id-input" placeholder="输入房间ID">
                    <button id="join-room-button">加入房间</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 等待界面 -->
    <div class="waiting-room" id="waiting-room" style="display: none;">
        <div class="waiting-content">
            <h2>房间: <span id="room-id-display"></span></h2>
            <p>玩家状态:</p>
            <div class="player-status">
                <div class="player" id="player1-status">
                    <span class="player-name">玩家1</span>
                    <span class="player-ready" id="player1-ready">未准备</span>
                </div>
                <div class="player" id="player2-status">
                    <span class="player-name">等待玩家加入...</span>
                    <span class="player-ready" id="player2-ready"></span>
                </div>
            </div>
            <button id="ready-button">准备</button>
            <p id="waiting-message">等待另一位玩家加入并准备...</p>
        </div>
    </div>
    
    <!-- 游戏界面 -->
    <div class="game-container" id="game-container" style="display: none;">
        <div class="player-boards">
            <!-- 玩家1的游戏板 -->
            <div class="player-board">
                <h2>你的游戏</h2>
                <div class="game-info">
                    <div class="score-container">
                        <p>分数: <span id="score">0</span></p>
                        <p>等级: <span id="level">1</span></p>
                        <p>已消除行数: <span id="lines">0</span></p>
                    </div>
                    <div class="next-piece-container">
                        <p>下一个方块:</p>
                        <div class="next-piece" id="next-piece"></div>
                    </div>
                </div>
                <div class="game-board-container">
                    <div class="game-board" id="game-board"></div>
                </div>
            </div>
            
            <!-- 玩家2的游戏板 -->
            <div class="player-board">
                <h2>对手的游戏</h2>
                <div class="game-info">
                    <div class="score-container">
                        <p>分数: <span id="opponent-score">0</span></p>
                        <p>等级: <span id="opponent-level">1</span></p>
                        <p>已消除行数: <span id="opponent-lines">0</span></p>
                    </div>
                </div>
                <div class="game-board-container">
                    <div class="game-board" id="opponent-board"></div>
                </div>
            </div>
        </div>
        
        <div class="game-controls">
            <button id="start-button">开始/暂停</button>
            <div class="instructions">
                <h3>操作说明:</h3>
                <p>← → : 左右移动</p>
                <p>↑ : 旋转</p>
                <p>↓ : 加速下落</p>
                <p>空格 : 直接落到底部</p>
            </div>
        </div>
    </div>
    
    <!-- 游戏结束界面 -->
    <div class="game-over" id="game-over" style="display: none;">
        <div class="game-over-content">
            <h2 id="game-result">游戏结束!</h2>
            <p>你的分数: <span id="final-score">0</span></p>
            <button id="play-again">返回大厅</button>
        </div>
    </div>
    
    <!-- 连接状态提示 -->
    <div class="connection-status" id="connection-status">
        <p>连接中...</p>
    </div>
    
    <!-- 错误提示 -->
    <div class="error-message" id="error-message" style="display: none;">
        <p id="error-text"></p>
        <button id="error-close">关闭</button>
    </div>
    
    <script src="script.js"></script>
</body>
</html>